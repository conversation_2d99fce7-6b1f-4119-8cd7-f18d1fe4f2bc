body {
  background: #f5f6fa !important;
}

.MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline {
  border-color: #e0e0e0;
  top: 0;
  border-radius: 10px !important;
}
.MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline .css-w1u3ce {
  display: none !important;
}
.css-m2bhuk-MuiInputBase-root-MuiOutlinedInput-root:hover,
.css-uufuzg-MuiInputBase-root-MuiOutlinedInput-root:hover,
.MuiInputBase-root.MuiOutlinedInput-root:hover {
  background: unset !important;
}
.MuiOutlinedInput-root {
  height: 45px !important;
  border: none !important;
  box-shadow: none !important;
}
.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}
.MuiInputLabel-outlined {
  transform: translate(14px, 16px) scale(1);
}
.MuiFormControl-root.MuiTextField-root {
  border: 1px solid #979797;
  border-radius: 10px !important;
  overflow: hidden;
}
.MuiSelect-root {
  border: 1px solid #979797 !important;
  border-radius: 10px !important;
}
.MuiFormControl-root.MuiTextField-root.css-t20anr-MuiFormControl-root-MuiTextField-root {
  border: none !important;
}
.MuiFormControl-root.MuiTextField-root:has(.Mui-error) .MuiOutlinedInput-root {
  border: 1px solid #979797 !important;
  border-radius: 10px !important;
}
.MuiFormControl-root.MuiTextField-root:has(.Mui-error) {
  border: none;
  border-radius: 0;
}
.MuiOutlinedInput-notchedOutline {
  border: none !important;
}
fieldset {
  /* border: none !important; */
  box-shadow: none !important;
}
.MuiInputLabel-outlined.MuiInputLabel-shrink {
  background-color: #fff;
  position: relative;
  transform: translate(0, -9px) scale(0.75);
}
.ck.ck-content.ck-editor__editable {
  height: 200px;
}
.json-editor {
  background: #fff !important;
}
/* ul.list-item-sidebar:nth-child(2){
        padding-bottom: 20px;
        border-bottom: 1px solid #E0E0E0;
    } */
ul.list-item-sidebar:nth-child(1) a,
ul.list-item-sidebar:nth-child(2) a {
  padding-left: 20px;
}
/* ul.list-item-sidebar:nth-child(1),
    ul.list-item-sidebar:nth-child(2),
    ul.list-item-sidebar:nth-child(1) a span,
    ul.list-item-sidebar:nth-child(2) a span{
        font-weight: 700;
        padding-left: 0;
    } */
.MuiInputBase-formControl fieldset {
  display: none;
}
.MuiFormControl-root label {
  position: relative;
  transform: none !important;
  margin-bottom: 5px;
}
.MuiFormControl-root:has(label) {
  border: none !important;
}
.MuiFormControl-root:has(label) input {
  border: 1px solid #979797;
  border-radius: 10px;
}
.MuiSelect-root:has(.MuiSelect-select) {
  border-radius: 10px !important;
}

.public-DraftStyleDefault-block.text-align-left {
  text-align: left;
}

.public-DraftStyleDefault-block.text-align-center {
  text-align: center;
}

.public-DraftStyleDefault-block.text-align-right {
  text-align: right;
}

/* Draft.js text alignment styles */
.public-DraftStyleDefault-block[data-text-align="left"] {
  text-align: left;
}

.public-DraftStyleDefault-block[data-text-align="center"] {
  text-align: center;
}

.public-DraftStyleDefault-block[data-text-align="right"] {
  text-align: right;
}

/* Template variable styling for Contenido */
.template-variable {
  background: rgb(48, 114, 255);
  border-radius: 4px;
  color: rgb(255, 255, 255);
  padding: 0px 4px;
  display: inline-block;
  margin: 0px;
}
