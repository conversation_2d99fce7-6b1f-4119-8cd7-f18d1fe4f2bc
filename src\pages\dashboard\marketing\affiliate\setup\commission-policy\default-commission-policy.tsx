import React, { useState } from 'react';
import { Box, Typography, TextField, Paper, Avatar, Link } from '@mui/material';
import { styled } from '@mui/material/styles';

const CustomBox = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: 12,
  boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
}));

const ExampleBox = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: 12,
  background: '#F9F9F9',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

const Row = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: 10,
});

const CommissionSettings = () => {
  const [level1, setLevel1] = useState(10);
  const [level2, setLevel2] = useState(5);

  return (
    <Box sx={{ width: '100%', padding: '70px 15px' }}>
      <Link
        href="/dashboard/marketing/affiliate/setup/commission-policy/commission-policy"
        sx={{
          fontSize: '20px',
          textDecoration: 'none',
          fontWeight: '700',
          color: '#000',
          paddingBottom: '30px',
        }}
      >
        {' '}
        {'< Chính sách hoa hồng mặc định'}
      </Link>
      <Box
        display="flex"
        gap={4}
        marginTop={'30px'}
        sx={{ '@media (max-width: 1180px)': { flexDirection: 'column' } }}
      >
        {/* Chính sách hoa hồng */}
        <CustomBox sx={{ width: '70%', '@media (max-width: 1180px)': { width: '100%' } }}>
          <Typography variant="h6" fontWeight="bold">
            Chính sách hoa hồng mặc định
          </Typography>
          <Typography variant="body2" color="textSecondary" mb={2}>
            Đặt mức hoa hồng phù hợp với ngân sách tiếp thị của bạn, có thể hỗ trợ hoa hồng cấp 2
          </Typography>

          <Row
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '30px',
            }}
          >
            <Typography sx={{ fontSize: '16px', fontWeight: '500', color: '#000' }}>
              Hoa hồng bậc 1
            </Typography>
            <TextField
              value={level1}
              onChange={(e) => setLevel1(Number(e.target.value) || 0)}
              type="number"
              size="small"
              sx={{
                width: '20%',
                padding: '0px !important',
                overflow: 'hidden',
                '@media (max-width: 880px)': { width: '30%' },
                '& .MuiInputBase-root': { padding: 0, overflow: 'hidden' },
                '& input': { padding: '0 0 0 10px !important', overflow: 'hidden' },
              }}
              InputProps={{
                endAdornment: (
                  <Typography
                    sx={{
                      background: '#efefef',
                      width: '20%',
                      padding: '5px 10px',
                      '@media (max-width: 880px)': { width: '50%' },
                    }}
                  >
                    %
                  </Typography>
                ),
              }}
            />
          </Row>

          <Row sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography sx={{ fontSize: '16px', fontWeight: '500', color: '#000' }}>
              Hoa hồng bậc 2
            </Typography>
            <TextField
              value={level2}
              onChange={(e) => setLevel2(Number(e.target.value) || 0)}
              type="number"
              size="small"
              sx={{
                width: '20%',
                padding: '0px !important',
                overflow: 'hidden',
                '@media (max-width: 880px)': { width: '30%' },
                '& .MuiInputBase-root': { padding: 0, overflow: 'hidden' },
                '& input': { padding: '0 0 0 10px !important', overflow: 'hidden' },
              }}
              InputProps={{
                endAdornment: (
                  <Typography
                    sx={{
                      background: '#efefef',
                      width: '20%',
                      padding: '5px 10px',
                      '@media (max-width: 880px)': { width: '50%' },
                    }}
                  >
                    %
                  </Typography>
                ),
              }}
            />
          </Row>
        </CustomBox>

        <ExampleBox flex={1} sx={{ width: '30%', '@media (max-width: 1180px)': { width: '100%' } }}>
          <Typography
            variant="h6"
            fontWeight="400"
            sx={{
              fontSize: '20px',
              color: '#000',
              padding: '10px 10px',
              background: '#D9D9D9',
              borderRadius: '50px',
              width: 'fit-content',
            }}
          >
            Ví dụ minh họa
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Row sx={{ display: 'flex', alignItems: 'start', justifyContent: 'start' }}>
              <Box
                sx={{
                  width: '20%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  flexDirection: 'column',
                }}
              >
                <Avatar sx={{ width: 32, height: 32 }} src="https://i.pravatar.cc/32?img=1" />
                <Typography
                  variant="body2"
                  sx={{ fontSize: '14px', fontWeight: '400', color: '#000', textAlign: 'center' }}
                >
                  Người tiêu dùng
                </Typography>
                <Box>
                  <svg
                    width="24"
                    height="48"
                    viewBox="0 0 24 48"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <polygon points="12,0 0,12 24,12" fill="#A0A0A0" />
                    <line
                      x1="12"
                      y1="14"
                      x2="12"
                      y2="46"
                      stroke="#A0A0A0"
                      stroke-width="2"
                      stroke-dasharray="4 4"
                    />
                  </svg>
                </Box>
              </Box>
              <Box
                sx={{
                  width: '80%',
                  background: '#fff',
                  padding: '10px',
                  borderRadius: '15px 15px 15px 0px',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{ fontSize: '14px', fontWeight: '400', color: '#000' }}
                >
                  Sau khi bạn mua sắm số tiền thực tế bạn trả khi đã trừ chiết khấu, phiếu giảm giá,
                  phí vận chuyển là 100.000đ
                </Typography>
              </Box>
            </Row>

            <Row sx={{ display: 'flex', alignItems: 'center', justifyContent: 'start' }}>
              <Box
                sx={{
                  width: '20%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  flexDirection: 'column',
                }}
              >
                <Avatar sx={{ width: 28, height: 28 }} src="https://i.pravatar.cc/28?img=2" />
                <Typography variant="body2" textAlign={'center'}>
                  Cấp độ 1:
                </Typography>
                <Box>
                  <svg
                    width="24"
                    height="48"
                    viewBox="0 0 24 48"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <polygon points="12,0 0,12 24,12" fill="#A0A0A0" />
                    <line
                      x1="12"
                      y1="14"
                      x2="12"
                      y2="46"
                      stroke="#A0A0A0"
                      stroke-width="2"
                      stroke-dasharray="4 4"
                    />
                  </svg>
                </Box>
              </Box>
              <Box
                sx={{
                  width: 'fit-content',
                  background: '#fff',
                  padding: '10px',
                  borderRadius: '15px 15px 15px 0px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                    paddingRight: '15px',
                    borderRight: '1px solid #D9D9D9',
                  }}
                >
                  Mức 1:{' '}
                  <Typography
                    variant="body2"
                    sx={{ fontSize: '14px', fontWeight: '400', color: '#000' }}
                  >
                    {level1}%
                  </Typography>
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    marginLeft: '10px',
                    color: '#2654FE',
                    borderRadius: '50px',
                    padding: '5px 10px',
                    background: '#2654FE1A',
                    fontWeight: '500',
                  }}
                >
                  ₫ {((level1 / 100) * 100000).toLocaleString()}
                </Typography>
              </Box>
            </Row>

            <Row sx={{ display: 'flex', alignItems: 'center', justifyContent: 'start' }}>
              <Box
                sx={{
                  width: '20%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  flexDirection: 'column',
                }}
              >
                <Avatar sx={{ width: 28, height: 28 }} src="https://i.pravatar.cc/28?img=3" />
                <Typography variant="body2" textAlign={'center'}>
                  Cấp độ 2:
                </Typography>
              </Box>
              <Box
                sx={{
                  width: 'fit-content',
                  background: '#fff',
                  padding: '10px',
                  borderRadius: '15px 15px 15px 0px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                    paddingRight: '15px',
                    borderRight: '1px solid #D9D9D9',
                  }}
                >
                  Mức 2:{' '}
                  <Typography
                    variant="body2"
                    sx={{ fontSize: '14px', fontWeight: '400', color: '#000' }}
                  >
                    {level2}%
                  </Typography>
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    marginLeft: '10px',
                    color: '#2654FE',
                    borderRadius: '50px',
                    padding: '5px 10px',
                    background: '#2654FE1A',
                    fontWeight: '500',
                  }}
                >
                  ₫ {((level2 / 100) * 100000).toLocaleString()}
                </Typography>
              </Box>
            </Row>
          </Box>

          <Row
            sx={{
              paddingTop: '20px',
              borderTop: '1px solid #D9D9D9',
              textAlign: 'center',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography
              sx={{
                display: 'flex',
                alignItems: 'center',
                fontWeight: '400',
                gap: '10px',
                textAlign: 'center',
              }}
            >
              Tổng hoa hồng phải trả:{' '}
              <Typography
                variant="body2"
                sx={{ fontSize: '14px', fontWeight: '700', color: '#000' }}
              >
                15.000 đ
              </Typography>
            </Typography>
          </Row>
        </ExampleBox>
      </Box>
    </Box>
  );
};

export default CommissionSettings;
