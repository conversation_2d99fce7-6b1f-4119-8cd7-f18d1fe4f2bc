import { useMedia } from "@/src/api/hooks/media/use-media";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { ProfileService } from "@/src/api/services/profile/profile.service";
import { ShopDto } from "@/src/api/services/shop/shop.service";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { FileType } from "@/src/constants/file-types";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import StoreLayout from "@/src/layouts/store/store-layout";
import { paths } from "@/src/paths";
import { ImageProcessor } from "@/src/utils/image-processor";
import { Box, Button, Card, CardContent, MenuItem, TextField } from "@mui/material";
import { useFormik } from "formik";
import { useRouter } from "next/router";
import { StorageService } from "nextjs-api-lib";
import React, { ReactElement, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import LogoUpload from "src/components/logo-upload";
import { tokens } from "src/locales/tokens";
import * as Yup from "yup";

type BusinessType = {
  name: string;
  label: string;
  description: string;
  status: string;
  createdDate: string;
  modifiedDate: string | null;
  createdBy: string;
  modifiedBy: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  id: string;
};

const CreateStorePage: React.FC & {
  getLayout?: (page: ReactElement) => ReactElement;
} = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const snackbar = useSnackbar();
  const { createShop, updateLogo, loading, error } = useShop();
  const [partnerId, setPartnerId] = useState("");
  const [shopLogo, setShopLogo] = useState<File | null>(null);
  const [validateLogoError, setValidateLogoError] = useState(false);
  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups, uploadFile } = useMedia();

  const storeId = useStoreId();

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);
  useEffect(() => {
    const storedPartnerId = StorageService.get("partnerId") || "";
    if (storedPartnerId) {
      setPartnerId(storedPartnerId as string);
    }
  }, []);

  useEffect(() => {
    const fetchBusinessTypes = async () => {
      try {
        const res = await ProfileService.getBusinessTypes();
        if (res && Array.isArray(res.data)) {
          setBusinessTypes(res.data);
        } else if (Array.isArray(res)) {
          setBusinessTypes(res);
        }
      } catch (err) {}
    };
    fetchBusinessTypes();
  }, []);

  const validationSchema = Yup.object({
    shopName: Yup.string().required(t(tokens.store.storeNameRequired)),
    businessType: Yup.string().required(t(tokens.store.businessTypeRequired)),
  });

  const formik = useFormik({
    initialValues: {
      shopName: "",
      businessType: "",
    },
    validationSchema,
    validateOnBlur: true,
    validateOnChange: true,
    onSubmit: async (values, { setSubmitting, setTouched }) => {
      try {
        setTouched({
          shopName: true,
          businessType: true,
        });

        const errors = await formik.validateForm();
        if (Object.keys(errors).length > 0) {
          return;
        }
        if (!shopLogo) {
          setValidateLogoError(true);
          return;
        }
        const processedFile = await ImageProcessor.processImage(shopLogo);
        const data: CreateFileGroupRequest = {
          FileUpload: processedFile,
          ShopId: storeId,
          GroupFileId: defaultGroupId,
          RefType: RefType.Shop,
          RefId: "",
        };
        const res = await uploadFile(data);
        if (res?.status === 200) {
          if (!shopLogo) {
            setValidateLogoError(true);
            return;
          }

          const shopData: ShopDto = {
            shopId: "",
            partnerId,
            oaId: "",
            businessType: values.businessType,
            shopName: values.shopName,
            shopSlogan: "",
            shopDesc: "",
            shopInfo: "",
            shopLogo: {
              mediaFileId: res?.data?.mediaFileId,
              link: res?.data?.link,
              type: FileType.IMAGE,
            },
            shopDeeplink: "",
            startDate: new Date().toISOString(),
            endDate: null,
            openTime: "07:00:00",
            closeTime: "18:00:00",
            prefixCode: "",
            shopTheme: {
              primaryColor: "",
              secondaryColor: "",
              accentColor: "",
            },
            shipCost: 0,
            provinceId: "",
            provinceName: "",
            districtId: "",
            districtName: "",
            wardsId: "",
            wardsName: "",
            address: "",
            longitude: 0,
            latitude: 0,
            active: "Actived",
            status: "Actived",
            referralCode: "",
            transportPrice: 0,
            template: "",
            enableExpressDelivery: false,
            enableInShop: false,
            defaultTaxRate: 0,
          };

          const response = await createShop(shopData);
          if (response?.status === 200) {
            router.push(paths.store.index);
          }
        }
      } catch (err) {
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleLogoChange = (file: File | null) => {
    setShopLogo(file);
    formik.setFieldValue("logo", file);
    setValidateLogoError(!file);
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        flex: 1,
        minHeight: "100vh",
        overflow: "hidden",
      }}
    >
      <Card
        elevation={16}
        sx={{
          maxWidth: 800,
          width: "100%",
          mx: "auto",
          my: 3,
        }}
      >
        <CardContent>
          <TitleTypography sx={{ mb: 2 }}>{t(tokens.store.title)}</TitleTypography>
          <form noValidate onSubmit={formik.handleSubmit}>
            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", md: "row" },
                gap: 4,
                mt: 3,
                alignItems: "stretch",
              }}
            >
              <Box sx={{ flex: 1, display: "flex", justifyContent: "center" }}>
                <LogoUpload
                  onLogoChange={handleLogoChange}
                  setShopLogo={setShopLogo}
                  shopLogo={shopLogo}
                  showError={validateLogoError}
                  setShowError={setValidateLogoError}
                />
              </Box>
              <Box
                sx={{
                  flex: 2,
                  display: "flex",
                  flexDirection: "column",
                  gap: 2,
                  justifyContent: "center",
                }}
              >
                <TextField
                  autoFocus
                  label={t(tokens.store.storeNameLabel)}
                  fullWidth
                  placeholder={t(tokens.store.storeNameLabel)}
                  value={formik.values.shopName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  name="shopName"
                  error={!!(formik.touched.shopName && formik.errors.shopName)}
                  helperText={formik.touched.shopName && formik.errors.shopName}
                />
                <TextField
                  select
                  label={t(tokens.store.businessTypeLabel)}
                  value={formik.values.businessType}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  name="businessType"
                  fullWidth
                  placeholder={t(tokens.store.businessTypeLabel)}
                  error={!!(formik.touched.businessType && formik.errors.businessType)}
                  helperText={formik.touched.businessType && formik.errors.businessType}
                >
                  {businessTypes.map((type) => (
                    <MenuItem key={type.name} value={type.name}>
                      {type.label}
                    </MenuItem>
                  ))}
                </TextField>
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  type="submit"
                  disabled={loading}
                >
                  {t(tokens.store.createButton)}
                </Button>
              </Box>
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

CreateStorePage.getLayout = (page: ReactElement) => <StoreLayout>{page}</StoreLayout>;

export default CreateStorePage;
