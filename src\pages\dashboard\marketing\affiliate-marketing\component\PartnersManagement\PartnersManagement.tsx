import React, { useC<PERSON>back, useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Container,
  TextField,
  InputAdornment,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  Stack,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ActivePartnersTable from "./ActivePartnersTable";
import PendingPartnersTable from "./PendingPartnersTable";
import DownloadIcon from "@mui/icons-material/Download";
import { LocalizationProvider, DateRangePicker, DatePicker } from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import {
  GetAffiliationPartnerRequest,
  StatusAffiliationPartner,
} from "@/src/api/types/affiliation.type";
import _ from "lodash";
import dayjs from "dayjs";

export interface AffilateParterDto {
  affiliationStatus: string;
  bankAccountName: string | null;
  bankAccountNumber: string | null;
  bankName: string | null;
  created: string;
  email: string | null;
  fullname: string;
  identityCardNumber: string | null;
  language: string;
  paymentType: string;
  phoneNumber: string;
  referrerCode: string | null;
  referrerName: string | null;
  status: string;
  taxCode: string | null;
  userId: string;
}

const PartnerManagement = () => {
  const { getAffiliationPartner, exportExcelAffiliationPartner } = useAffiliation();
  const [affiliationPartner, setAffiliationPartner] = useState<any>([]);
  const storeId = useStoreId();

  const [, setChartTimeRange] = useState("day");

  const [selectedTab, setSelectedTab] = useState("active");
  const [searchQuery, setSearchQuery] = useState("");

  const [chartDateRange, setChartDateRange] = useState<any>([null, null]);

  const [open, setOpen] = useState(false);
  const [filterData, setFilterData] = useState<GetAffiliationPartnerRequest>();
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>();

  const fetchAffiliationPartner = async (
    currentPage,
    pageSize,
    selectedTab,
    data: GetAffiliationPartnerRequest
  ) => {
    if (selectedTab === "active") {
      data.AffiliationStatus = "Actived";
    } else {
      data.AffiliationStatus = "InActived";
    }
    data.PageIndex = currentPage + 1;
    data.PageSize = pageSize;
    const res = await getAffiliationPartner(data);
    if (res && res.data) {
      setAffiliationPartner(res.data?.data);
      setTotalCount(res?.data.total);
    }
  };

  const debouncedFetchAffiliationPartner = useCallback(
    _.debounce((currentPage, pageSize, selectedTab, data) => {
      fetchAffiliationPartner(currentPage, pageSize, selectedTab, data);
    }, 400), // Delay 400ms
    []
  );

  useEffect(() => {
    if (filterData?.ShopId) {
      debouncedFetchAffiliationPartner(page, rowsPerPage, selectedTab, filterData);
    }

    return () => {
      debouncedFetchAffiliationPartner.cancel();
    };
  }, [page, rowsPerPage, selectedTab, filterData]);
  useEffect(() => {
    if (storeId) {
      setFilterData((prevState) => {
        return { ...prevState, ShopId: storeId };
      });
    }
  }, [storeId]);

  const handleExportExcel = async () => {
    try {
      const response = await exportExcelAffiliationPartner(filterData);
      if (response) {
        const blob = new Blob([response.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "DanhSachDoiTac.xlsx"); // tên file
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Export Excel failed:", error);
    }
  };

  const handleChartStartDateChange = (newValue) => {
    if (newValue?.toString() !== "Invalid Date") {
      const endDate = chartDateRange[1];
      let updatedRange;
      const startD = newValue?.startOf("day").format("YYYY-MM-DD HH:mm:ss");
      if (!endDate || newValue.isAfter(endDate)) {
        updatedRange = [newValue, newValue];
        setFilterData((prev) => ({
          ...prev,
          FromDate: startD,
          ToDate: newValue?.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        }));
      } else {
        const endD = endDate.endOf("day").format("YYYY-MM-DD HH:mm:ss");
        updatedRange = [newValue, endDate];
        setFilterData((prev) => ({ ...prev, FromDate: startD, ToDate: endD }));
      }
      setChartDateRange(updatedRange);
    }
  };

  const handleChartEndDateChange = (newValue) => {
    const validDayjs = dayjs(newValue);

    if (validDayjs.isValid() && validDayjs.year() >= 1000) {
      const startDate = chartDateRange[0];
      const endD = newValue?.endOf("day").format("YYYY-MM-DD HH:mm:ss");

      let updatedRange;

      if (!startDate || newValue.isBefore(startDate, "day")) {
        updatedRange = [newValue, newValue];
        setFilterData((prev) => ({
          ...prev,
          FromDate: newValue?.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          ToDate: endD,
        }));
      } else {
        const startD = startDate?.startOf("day").format("YYYY-MM-DD HH:mm:ss");
        updatedRange = [startDate, newValue];
        setFilterData((prev) => ({ ...prev, FromDate: startD, ToDate: endD }));
      }
      setChartDateRange(updatedRange);
    }
  };
  return (
    <Container
      maxWidth={false}
      sx={{
        boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
        borderRadius: "15px",
        padding: "20px",
        margin: "20px 0",
        background: "#fff",
        width: "100%",
      }}
    >
      <Typography sx={{ fontSize: "20px", fontWeight: 700, marginBottom: 1.5 }}>
        Quản lý đối tác
      </Typography>

      <Box sx={{ mb: 2 }}>
        <ToggleButtonGroup
          value={selectedTab}
          exclusive
          onChange={(event: any, newValue) => {
            if (newValue !== null) {
              setSelectedTab(event.target.value);
              setPage(0);
            }
          }}
          sx={{
            margin: "0",
            padding: "0",
            border: "1px solid #ccc",
            borderRadius: "10px",
            overflow: "hidden",
            width: { xs: "100%", sm: "auto" },
          }}
        >
          <ToggleButton
            value="active"
            sx={{
              height: { xs: 40, sm: 30 },
              width: { xs: "50%", sm: "170px" },
              padding: { xs: "8px 4px", sm: "15px 2px" },
              fontSize: { xs: 13, sm: 14 },
              boxShadow: "none",
              border: "none",
              fontWeight: "400",
              color: selectedTab === "active" ? "#fff" : "#000",
              backgroundColor: selectedTab === "active" ? "#2654FE" : "#fff",
              borderRadius: "0",
              textTransform: "none",
              "&.Mui-selected": {
                backgroundColor: "#2654FE",
                color: "#fff",
                fontWeight: "400",
                "&:hover": {
                  backgroundColor: "#1E45D9",
                },
              },
            }}
          >
            Đang hoạt động
          </ToggleButton>

          <ToggleButton
            value="pending"
            sx={{
              height: { xs: 40, sm: 30 },
              width: { xs: "50%", sm: "170px" },
              padding: { xs: "8px 4px", sm: "15px 15px" },
              fontSize: { xs: 13, sm: 14 },
              boxShadow: "none",
              border: "none",
              fontWeight: "400",
              color: selectedTab === "pending" ? "#fff" : "#000",
              backgroundColor: selectedTab === "pending" ? "#2654FE" : "#fff",
              borderRadius: "0",
              textTransform: "none",
              "&.Mui-selected": {
                backgroundColor: "#2654FE",
                color: "#fff",
                fontWeight: "400",
                "&:hover": {
                  backgroundColor: "#1E45D9",
                },
              },
            }}
          >
            Chờ duyệt
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      <Stack
        direction={{ xs: "column", sm: "row" }}
        justifyContent="space-between"
        alignItems={{ xs: "stretch", sm: "center" }}
        gap={2}
        sx={{ width: "100%", mb: 2 }}
      >
        <Stack
          direction={{ xs: "column", sm: "row" }}
          alignItems={{ xs: "stretch", sm: "center" }}
          gap={1}
          sx={{ flex: 1 }}
        >
          <TextField
            variant="outlined"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setFilterData((prev) => ({ ...prev, SearchTerm: e.target.value }));
            }}
            placeholder="Tìm tên, mã đối tác"
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              width: { xs: "100%", sm: 280 },
              "& .MuiOutlinedInput-root": {
                borderRadius: "8px",
                height: 36,
                fontSize: 14,
              },
            }}
          />

          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              gap: 1,
              width: { xs: "100%", sm: "auto" },
            }}
          >
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
              <DatePicker
                value={chartDateRange[0]}
                format="DD/MM/YYYY"
                onChange={handleChartStartDateChange}
                slotProps={{
                  textField: {
                    size: "small",
                    sx: {
                      width: { xs: "100%", sm: 200 }, // Tăng từ 120 lên 140
                      flex: { xs: 1, sm: "none" },
                      "& .MuiOutlinedInput-root": {
                        height: 36,
                        borderRadius: "8px",
                        fontSize: 14,
                      },
                    },
                  },
                }}
              />
              <DatePicker
                value={chartDateRange[1]}
                format="DD/MM/YYYY"
                onChange={handleChartEndDateChange}
                shouldDisableDate={(date) => date.isBefore(chartDateRange[0], "day")}
                slotProps={{
                  textField: {
                    size: "small",
                    sx: {
                      width: { xs: "100%", sm: 200 }, // Tăng từ 120 lên 140
                      flex: { xs: 1, sm: "none" },
                      "& .MuiOutlinedInput-root": {
                        height: 36,
                        borderRadius: "8px",
                        fontSize: 14,
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </Box>
        </Stack>

        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={handleExportExcel}
          size="small"
          sx={{
            textTransform: "none",
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#2654FE",
            border: "1.5px solid #2654FE",
            fontSize: "14px",
            fontWeight: 600,
            minWidth: 60, // Tăng từ 45 lên 60
            width: { xs: "calc(50% - 2px)", sm: 150 }, // Tăng từ 90 lên 120
            height: 36,
            boxShadow: "none",
            transition: "all 0.2s",
            "&:hover": {
              backgroundColor: "#F0F6FF",
              borderColor: "#2654FE",
              color: "#2654FE",
            },
            "& .MuiButton-startIcon": {
              marginRight: 1,
            },
          }}
        >
          Export
        </Button>
      </Stack>

      {selectedTab === "active" ? (
        <ActivePartnersTable
          searchQuery={searchQuery}
          page={page}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          affiliationPartner={affiliationPartner}
          totalCount={totalCount}
          fetchAffiliationPartner={fetchAffiliationPartner}
        />
      ) : (
        <PendingPartnersTable
          searchQuery={searchQuery}
          page={page}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          affiliationPartner={affiliationPartner}
          totalCount={totalCount}
          fetchAffiliationPartner={fetchAffiliationPartner}
        />
      )}
    </Container>
  );
};

export default PartnerManagement;
