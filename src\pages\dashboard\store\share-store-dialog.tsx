import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  IconButton,
  Typography,
  Button,
  InputAdornment,
  TextField
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import { QRCodeCanvas } from 'qrcode.react';
import { useSnackbar } from '@/src/hooks/use-snackbar';

interface ShareStoreDialogProps {
  open: boolean;
  onClose: () => void;
  storeUrl: string;
}

const ShareStoreDialog = ({ open, onClose, storeUrl }: ShareStoreDialogProps) => {
  const snackbar = useSnackbar();

  const handleCopyUrl = () => {
    navigator.clipboard.writeText(storeUrl);
    snackbar.success('Đã sao chép link cửa hàng');
  };

  const handleDownloadQR = () => {
    const canvas = document.getElementById('store-qr-code') as HTMLCanvasElement;
    if (canvas) {
      const url = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.download = 'store-qr-code.png';
      link.href = url;
      link.click();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Chia sẻ</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 1 }}>
            Link chia sẻ
          </Typography>
          <TextField
            label="Link chia sẻ"
            fullWidth
            value={storeUrl}
            InputProps={{
              readOnly: true,
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleCopyUrl}>
                    <ContentCopyIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
          <QRCodeCanvas
            id="store-qr-code"
            value={storeUrl}
            size={200}
            level="H"
            includeMargin={true}
            style={{
              width: '100%',
              maxWidth: '200px',
              height: 'auto'
            }}
          />
          <Button startIcon={<DownloadIcon />} onClick={handleDownloadQR} sx={{ mt: 2 }}>
            Tải xuống mã QR
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ShareStoreDialog;
