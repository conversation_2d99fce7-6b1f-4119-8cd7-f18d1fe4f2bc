import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import {
  Box,
  Button,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  useTheme,
  useMediaQuery,
  Card,
  Checkbox,
} from "@mui/material";
import { tokens } from "@/src/locales/tokens";
import { EmptyState } from "@/src/components/common/empty-state";
import { LoadingState } from "@/src/components/common/loading-state";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import { useService } from "@/src/api/hooks/dashboard/product/use-service";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import DashboardLayout from "@/src/layouts/dashboard";
import { paths } from "@/src/paths";
import { SearchToolbar } from "../../../../components/components/search-toolbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { GetProductCategoryRequest, TreeCategory } from "@/src/api/types/product-category.types";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { useDebounce } from "@/src/hooks/use-debounce";
import { StorageService } from "nextjs-api-lib";
import { useAppSelector } from "@/src/redux/hooks";
import ProductItem from "./components/product-item";
const TAB_STORAGE_KEY = "productManagementTab";

export const getFirstVariant = (item: any) => {
  if (item.listVariant && item.listVariant.length > 0) {
    return item.listVariant[0];
  }
  return null;
};

const findCategoryById = (categories: TreeCategory[], categoryId: string): TreeCategory | null => {
  for (const category of categories) {
    if (category.categoryId === categoryId) {
      return category;
    }
    if (category.listSubCategory && category.listSubCategory.length > 0) {
      const found = findCategoryById(category.listSubCategory, categoryId);
      if (found) return found;
    }
  }
  return null;
};

export const getCategoryName = (categoryIds: string[], categories: TreeCategory[]) => {
  if (!Array.isArray(categories)) {
    return "";
  }

  const ids = Array.isArray(categoryIds) ? categoryIds : [categoryIds];

  const categoryNames = ids.map((id) => {
    const category = findCategoryById(categories, id);
    return category ? category.categoryName : "";
  });

  const newCategoryNames = categoryNames.filter((item) => item !== "");
  const result = newCategoryNames.join(", ");
  return result;
};

const ProductManagement = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  // Hooks
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const snackbar = useSnackbar();

  // Services
  const { getProduct, deleteProduct, loading: productLoading } = useProduct();
  const { getService, deleteService, loading: serviceLoading } = useService();
  const { getProductCategoryTree } = useProductCategory();
  const currentShop = useAppSelector((state) => state.shop.currentShop);
  // States
  const [items, setItems] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [subCategoryId, setSubCategoryId] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [tabValue, setTabValue] = useState(() => {
    // if (typeof window !== 'undefined') {
    //   return parseInt(localStorage.getItem(TAB_STORAGE_KEY) || '0');
    // }
    return 0;
  });
  const [isLoading, setIsLoading] = useState(true);
  const [categories, setCategories] = useState([]);
  const storeId = useStoreId();
  const mounted = useRef(false);
  const debouncedSearchValue = useDebounce(searchTerm, 500);

  const fetchItems = async () => {
    try {
      const service = tabValue === 0 ? getProduct : getService;
      const itemsType = tabValue === 0 ? "Product" : "Service";

      const response = await service(
        page * rowsPerPage,
        rowsPerPage,
        itemsType,
        categoryId || null,
        subCategoryId || null,
        debouncedSearchValue
      );

      setItems(response.data.data || []);
      setTotalCount(response.data.total || 0);
    } catch (error) {}
  };

  const fetchCategories = async () => {
    try {
      const params: GetProductCategoryRequest = {
        categoryType: tabValue === 0 ? "Product" : "Service",
        shopId: storeId,
        partnerId: StorageService.get("partnerId") as string | null,
        paging: {
          name: "Created",
          nameType: "Created",
          sortType: "desc",
          sort: "desc",
          pageIndex: 0,
          pageSize: 100,
        },
      };
      const response = await getProductCategoryTree(params);
      setCategories(response.data.data.result || []);
    } catch (error) {}
  };

  useEffect(() => {
    const initData = async () => {
      if (!storeId || mounted.current) return;

      setIsLoading(true);
      try {
        await Promise.all([fetchItems(), fetchCategories()]);
        mounted.current = true;
      } catch (error) {
      } finally {
        setIsLoading(false);
      }
    };

    initData();
  }, [storeId]);

  // Effect cho tab change
  useEffect(() => {
    const handleTabChange = async () => {
      if (!mounted.current || !storeId) return;

      setIsLoading(true);
      try {
        await Promise.all([fetchItems(), fetchCategories()]);
      } catch (error) {
        console.error("Error handling tab change:", error);
      } finally {
        setIsLoading(false);
      }
    };

    handleTabChange();
  }, [tabValue, storeId]);

  useEffect(() => {
    const handleFilters = async () => {
      if (!mounted.current) return;

      try {
        await fetchItems();
      } catch (error) {
        console.error("Error handling filters:", error);
      }
    };

    handleFilters();
  }, [page, rowsPerPage, debouncedSearchValue, categoryId, subCategoryId]);

  // Handlers
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(0);
    setSearchTerm("");
    setCategoryId("");
    setSubCategoryId("");
    setSelectedIds([]);
    localStorage.setItem(TAB_STORAGE_KEY, newValue.toString());
  };

  const handleEdit = (item: any) => {
    router.push({
      pathname: paths.dashboard.product.updateProduct(item.itemsCode),
      query: {
        type: tabValue === 0 ? "Product" : "Service",
      },
    });
  };

  const handleDelete = (item: any) => {
    setItemToDelete(item);
    setOpenDialog(true);
  };

  const confirmDelete = async () => {
    if (!itemToDelete) return;

    try {
      const service = tabValue === 0 ? deleteProduct : deleteService;
      await service(itemToDelete.itemsCode);

      snackbar.success(t(tokens.contentManagement.product.delete.successMessage));
      fetchItems();
    } catch (error) {
      //snackbar.error(t(tokens.contentManagement.product.delete.errorMessage));
    } finally {
      setOpenDialog(false);
      setItemToDelete(null);
    }
  };

  const handleCreate = () => {
    router.push(
      `${paths.dashboard.product.createProduct}?type=${tabValue === 0 ? "Product" : "Service"}`
    );
  };

  const getEmptyStateText = (tabValue: number) => {
    return {
      title: tabValue === 0 ? "Chưa có sản phẩm nào" : "Chưa có dịch vụ nào",
      subtitle:
        tabValue === 0
          ? "Tạo sản phẩm để bắt đầu bán cho khách hàng của bạn"
          : "Tạo dịch vụ để bắt đầu cung cấp cho khách hàng của bạn",
      buttonText: tabValue === 0 ? "Tạo sản phẩm mới" : "Tạo dịch vụ mới",
    };
  };

  // get Title by tab index
  const getTitleByTab = (tabIndex: number) => {
    return tabIndex === 0 ? "Hàng hoá/Sản phẩm" : "Hàng hoá/Dịch vụ";
  };

  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const isAllSelected = items.length > 0 && selectedIds.length === items.length;

  const handleCheck = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((itemId) => itemId !== id) : [...prev, id]
    );
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedIds(items.map((item) => item.itemsCode));
    } else {
      setSelectedIds([]);
    }
  };

  const renderContent = () => (
    <Card>
      <Box sx={{ overflowX: "auto" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={selectedIds.length > 0 && selectedIds.length < items.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell sx={{ pl: 3, minWidth: 50 }}>STT</TableCell>
              <TableCell sx={{ pl: 3, minWidth: 80 }}>{`Mã ${
                tabValue === 0 ? "sản phẩm" : "dịch vụ"
              }`}</TableCell>
              <TableCell sx={{ pl: 3, minWidth: 300 }}>
                {tabValue === 0
                  ? t(tokens.contentManagement.product.table.product)
                  : t(tokens.contentManagement.product.table.service)}
              </TableCell>
              <TableCell sx={{ minWidth: 50 }}>
                {t(tokens.contentManagement.product.table.price)}
              </TableCell>
              <TableCell sx={{ minWidth: 130 }}>
                {t(tokens.contentManagement.product.table.stock)}
              </TableCell>
              <TableCell sx={{ minWidth: 100 }}>
                {t(tokens.contentManagement.product.table.status)}
              </TableCell>
              <TableCell sx={{ minWidth: 150 }}>
                {tabValue === 0
                  ? t(tokens.contentManagement.product.table.featured)
                  : t(tokens.contentManagement.product.table.featuredService)}
              </TableCell>
              <TableCell sx={{ width: 150 }}>
                {t(tokens.contentManagement.product.table.category)}
              </TableCell>
              <TableCell
                sx={{
                  minWidth: 100,
                  pr: 3,
                  position: "sticky",
                  right: 0,
                  bottom: 0,
                  backgroundColor: "#fff",
                  zIndex: 3,
                  boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                  padding: { xs: "16px 4px", sm: "20px 16px" },
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  width: { xs: "70px", sm: "90px" },
                }}
              >
                {t(tokens.contentManagement.product.table.actions)}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map((item: any, index: number) => (
              <ProductItem
                key={item.itemsCode}
                item={item}
                index={index}
                page={page}
                selectedIds={selectedIds}
                handleCheck={handleCheck}
                handleEdit={handleEdit}
                handleDelete={handleDelete}
                categories={categories}
                pathname={pathname}
                tabValue={tabValue}
                currentShop={currentShop}
                t={t}
                isGranted={isGranted}
              />
            ))}
          </TableBody>
        </Table>
      </Box>
      <TablePagination
        labelRowsPerPage="Số hàng mỗi trang"
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={rowPerPageOptionsDefault}
      />
    </Card>
  );

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        <TitleTypography
          sx={{
            fontSize: "20px !important",
            lineHeight: "1",
            fontWeight: "700",
            paddingBottom: "20px",
            marginBottom: "16px",
            borderBottom: "1px solid #bdbdbd",
          }}
        >
          {getTitleByTab(tabValue)}
        </TitleTypography>
        <Card sx={{ p: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              mb: 3,
              "& .MuiTabs-indicator": {
                background: "#2654FE",
              },
            }}
          >
            <Tab
              sx={{
                textTransform: "none !important",
                color: "#000",
                "&.Mui-selected": {
                  color: "#2654FE !important",
                  height: "3px",
                },
              }}
              label={t(tokens.contentManagement.product.tabs.product)}
            />
            <Tab
              sx={{
                textTransform: "none !important",
                color: "#000",
                "&.Mui-selected": {
                  color: "#2654FE !important",
                  height: "3px",
                },
              }}
              label={t(tokens.contentManagement.product.tabs.service)}
            />
          </Tabs>

          <SearchToolbar
            isGranted={isGranted}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            onAddClick={handleCreate}
            placeholder="Tìm theo tên"
            addButtonText={t(tokens.contentManagement.product.actions.create)}
            categories={categories}
            categoryId={categoryId}
            subCategoryId={subCategoryId}
            onCategoryChange={setCategoryId}
            onSubCategoryChange={setSubCategoryId}
            tabValue={tabValue}
            listItem={items}
            fetchData={fetchItems}
            selectedIds={selectedIds}
            setSelectedIds={setSelectedIds}
          />

          {isLoading ? (
            <LoadingState />
          ) : items.length === 0 ? (
            <EmptyState
              title={getEmptyStateText(tabValue).title}
              subtitle={getEmptyStateText(tabValue).subtitle}
              buttonText={getEmptyStateText(tabValue).buttonText}
              onAddClick={handleCreate}
              isGranted={isGranted}
            />
          ) : (
            renderContent()
          )}
        </Card>

        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>{t(tokens.contentManagement.product.delete.confirmTitle)}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {t(tokens.contentManagement.product.delete.confirmMessage, {
                name: itemToDelete?.itemsName,
              })}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)} variant="outlined">
              {t(tokens.common.cancel)}
            </Button>
            <Button onClick={confirmDelete} color="error" variant="contained" autoFocus>
              {t(tokens.common.delete)}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </DashboardLayout>
  );
};

export default ProductManagement;
